@model List<ShoeStoreWeb.Models.CartItem>
@{
    ViewData["Title"] = "Giỏ hàng";
    var total = Model.Sum(c => c.Price * c.Quantity);
}

<div class="container">
    <div class="row">
        <div class="col-12">
            <h2><i class="fas fa-shopping-cart me-2"></i>Giỏ hàng của bạn</h2>
            <hr>
        </div>
    </div>

    @if (Model.Any())
    {
        <div class="row">
            <div class="col-lg-8">
                @foreach (var item in Model)
                {
                    <div class="card mb-3">
                        <div class="row g-0">
                            <div class="col-md-3">
                                <img src="@item.Image" class="img-fluid rounded-start h-100" alt="@item.ProductName" style="object-fit: cover;">
                            </div>
                            <div class="col-md-9">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h5 class="card-title">@item.ProductName</h5>
                                            <p class="text-muted mb-1">@item.ProductBrand</p>
                                            <p class="mb-1"><strong>Size:</strong> @item.Size</p>
                                            <p class="mb-1"><strong>Màu:</strong> @item.Color</p>
                                            <p class="h5 text-primary">@item.Price.ToString("N0") ₫</p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <div class="mb-3">
                                                <label class="form-label">Số lượng</label>
                                                <div class="input-group" style="width: 120px; margin-left: auto;">
                                                    <button class="btn btn-outline-secondary btn-sm quantity-btn" 
                                                            data-action="decrease" 
                                                            data-product-id="@item.ProductId" 
                                                            data-size="@item.Size" 
                                                            data-color="@item.Color">-</button>
                                                    <input type="number" class="form-control text-center quantity-input" 
                                                           value="@item.Quantity" min="1" 
                                                           data-product-id="@item.ProductId" 
                                                           data-size="@item.Size" 
                                                           data-color="@item.Color">
                                                    <button class="btn btn-outline-secondary btn-sm quantity-btn" 
                                                            data-action="increase" 
                                                            data-product-id="@item.ProductId" 
                                                            data-size="@item.Size" 
                                                            data-color="@item.Color">+</button>
                                                </div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Thành tiền: @((item.Price * item.Quantity).ToString("N0")) ₫</strong>
                                            </div>
                                            <button class="btn btn-danger btn-sm remove-item-btn" 
                                                    data-product-id="@item.ProductId" 
                                                    data-size="@item.Size" 
                                                    data-color="@item.Color">
                                                <i class="fas fa-trash me-1"></i>Xóa
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Tóm tắt đơn hàng</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tạm tính:</span>
                            <span id="subtotal">@total.ToString("N0") ₫</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Phí vận chuyển:</span>
                            <span class="text-success">Miễn phí</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Tổng cộng:</strong>
                            <strong class="text-primary" id="total">@total.ToString("N0") ₫</strong>
                        </div>
                        
                        @if (User.Identity!.IsAuthenticated)
                        {
                            <a href="@Url.Action("Checkout", "Orders")" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-credit-card me-2"></i>Thanh toán
                            </a>
                        }
                        else
                        {
                            <a href="@Url.Action("Login", "Account")" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập để thanh toán
                            </a>
                        }
                        
                        <a href="@Url.Action("Index", "Products")" class="btn btn-outline-primary w-100">
                            <i class="fas fa-arrow-left me-2"></i>Tiếp tục mua sắm
                        </a>
                        
                        <button class="btn btn-outline-danger w-100 mt-2" id="clear-cart-btn">
                            <i class="fas fa-trash me-2"></i>Xóa toàn bộ giỏ hàng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">Giỏ hàng của bạn đang trống</h4>
            <p class="text-muted">Hãy thêm một số sản phẩm vào giỏ hàng để tiếp tục mua sắm.</p>
            <a href="@Url.Action("Index", "Products")" class="btn btn-primary">
                <i class="fas fa-shopping-bag me-2"></i>Bắt đầu mua sắm
            </a>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Update quantity
            $('.quantity-btn').click(function() {
                var action = $(this).data('action');
                var productId = $(this).data('product-id');
                var size = $(this).data('size');
                var color = $(this).data('color');
                var input = $(this).siblings('.quantity-input');
                var currentQty = parseInt(input.val());
                
                var newQty = action === 'increase' ? currentQty + 1 : Math.max(1, currentQty - 1);
                input.val(newQty);
                
                updateQuantity(productId, size, color, newQty);
            });

            $('.quantity-input').change(function() {
                var productId = $(this).data('product-id');
                var size = $(this).data('size');
                var color = $(this).data('color');
                var quantity = Math.max(1, parseInt($(this).val()) || 1);
                
                $(this).val(quantity);
                updateQuantity(productId, size, color, quantity);
            });

            // Remove item
            $('.remove-item-btn').click(function() {
                var productId = $(this).data('product-id');
                var size = $(this).data('size');
                var color = $(this).data('color');
                
                if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?')) {
                    removeFromCart(productId, size, color);
                }
            });

            // Clear cart
            $('#clear-cart-btn').click(function() {
                if (confirm('Bạn có chắc chắn muốn xóa toàn bộ giỏ hàng?')) {
                    clearCart();
                }
            });
        });

        function updateQuantity(productId, size, color, quantity) {
            $.post('@Url.Action("UpdateQuantity", "Cart")', {
                productId: productId,
                size: size,
                color: color,
                quantity: quantity
            }, function(data) {
                if (data.success) {
                    location.reload();
                }
            });
        }

        function removeFromCart(productId, size, color) {
            $.post('@Url.Action("RemoveFromCart", "Cart")', {
                productId: productId,
                size: size,
                color: color
            }, function(data) {
                if (data.success) {
                    location.reload();
                }
            });
        }

        function clearCart() {
            $.post('@Url.Action("ClearCart", "Cart")', function(data) {
                if (data.success) {
                    location.reload();
                }
            });
        }
    </script>
}
