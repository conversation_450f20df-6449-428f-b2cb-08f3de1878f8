{"format": 1, "restore": {"D:\\duyht\\BTL CNTT\\ASP.NET\\DK23TTC10\\website bán giày\\nhan.website ban giay asp.net\\ShoeStoreWeb\\ShoeStoreWeb.csproj": {}}, "projects": {"D:\\duyht\\BTL CNTT\\ASP.NET\\DK23TTC10\\website bán giày\\nhan.website ban giay asp.net\\ShoeStoreWeb\\ShoeStoreWeb.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\duyht\\BTL CNTT\\ASP.NET\\DK23TTC10\\website b<PERSON> gi<PERSON>y\\nhan.website ban giay asp.net\\ShoeStoreWeb\\ShoeStoreWeb.csproj", "projectName": "ShoeStoreWeb", "projectPath": "D:\\duyht\\BTL CNTT\\ASP.NET\\DK23TTC10\\website b<PERSON> gi<PERSON>y\\nhan.website ban giay asp.net\\ShoeStoreWeb\\ShoeStoreWeb.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\duyht\\BTL CNTT\\ASP.NET\\DK23TTC10\\website bán gi<PERSON>y\\nhan.website ban giay asp.net\\ShoeStoreWeb\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}