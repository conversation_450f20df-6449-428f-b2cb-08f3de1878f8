@model IEnumerable<ShoeStoreWeb.Models.Order>
@{
    ViewData["Title"] = "Quản lý đơn hàng";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-shopping-cart me-2"></i>Quản lý đơn hàng</h1>
                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Về dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Danh sách đơn hàng (@Model.Count() đơn hàng)</h6>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Mã đơn hàng</th>
                                        <th>Khách hàng</th>
                                        <th>Ngày đặt</th>
                                        <th>Tổng tiền</th>
                                        <th>Trạng thái</th>
                                        <th>Phương thức thanh toán</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var order in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div class="fw-bold">#@(order.Id.Length > 8 ? order.Id.Substring(0, 8) : order.Id)</div>
                                                <small class="text-muted">@order.Items.Count sản phẩm</small>
                                            </td>
                                            <td>
                                                <div class="fw-bold">@order.UserName</div>
                                                <small class="text-muted">@order.User?.Email</small>
                                            </td>
                                            <td>@order.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>
                                                <div class="fw-bold text-primary">@order.Total.ToString("N0") ₫</div>
                                            </td>
                                            <td>
                                                @switch (order.Status)
                                                {
                                                    case "pending":
                                                        <span class="badge bg-warning text-dark">Chờ xử lý</span>
                                                        break;
                                                    case "confirmed":
                                                        <span class="badge bg-info">Đã xác nhận</span>
                                                        break;
                                                    case "shipping":
                                                        <span class="badge bg-primary">Đang giao</span>
                                                        break;
                                                    case "delivered":
                                                        <span class="badge bg-success">Đã giao</span>
                                                        break;
                                                    case "cancelled":
                                                        <span class="badge bg-danger">Đã hủy</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@order.Status</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                @switch (order.PaymentMethod)
                                                {
                                                    case "COD":
                                                        <span class="badge bg-light text-dark">Thanh toán khi nhận hàng</span>
                                                        break;
                                                    case "Bank":
                                                        <span class="badge bg-info">Chuyển khoản</span>
                                                        break;
                                                    case "Card":
                                                        <span class="badge bg-primary">Thẻ tín dụng</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@order.PaymentMethod</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-info view-order-btn" 
                                                            data-order-id="@order.Id"
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#orderDetailModal">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    @if (order.Status != "delivered" && order.Status != "cancelled")
                                                    {
                                                        <div class="btn-group" role="group">
                                                            <button class="btn btn-sm btn-outline-primary dropdown-toggle" 
                                                                    type="button" 
                                                                    data-bs-toggle="dropdown">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item update-status-btn" 
                                                                       data-order-id="@order.Id" 
                                                                       data-status="confirmed">Xác nhận</a></li>
                                                                <li><a class="dropdown-item update-status-btn" 
                                                                       data-order-id="@order.Id" 
                                                                       data-status="shipping">Đang giao</a></li>
                                                                <li><a class="dropdown-item update-status-btn" 
                                                                       data-order-id="@order.Id" 
                                                                       data-status="delivered">Đã giao</a></li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li><a class="dropdown-item text-danger update-status-btn" 
                                                                       data-order-id="@order.Id" 
                                                                       data-status="cancelled">Hủy đơn</a></li>
                                                            </ul>
                                                        </div>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">Chưa có đơn hàng nào</h4>
                            <p class="text-muted">Các đơn hàng sẽ hiển thị tại đây khi khách hàng đặt hàng.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Detail Modal -->
<div class="modal fade" id="orderDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chi tiết đơn hàng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailContent">
                <!-- Order details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Update order status
            $('.update-status-btn').click(function() {
                var orderId = $(this).data('order-id');
                var status = $(this).data('status');
                var statusText = $(this).text();
                
                if (confirm('Bạn có chắc chắn muốn cập nhật trạng thái đơn hàng thành "' + statusText + '"?')) {
                    $.post('@Url.Action("UpdateOrderStatus")', {
                        orderId: orderId,
                        status: status
                    }, function(data) {
                        if (data.success) {
                            showToast('Cập nhật trạng thái đơn hàng thành công!', 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            showToast(data.message || 'Có lỗi xảy ra', 'error');
                        }
                    });
                }
            });

            // View order details (placeholder)
            $('.view-order-btn').click(function() {
                var orderId = $(this).data('order-id');
                $('#orderDetailContent').html('<div class="text-center py-3"><i class="fas fa-spinner fa-spin"></i> Đang tải...</div>');
                
                // In a real application, you would load order details via AJAX
                setTimeout(function() {
                    $('#orderDetailContent').html(
                        '<div class="alert alert-info">' +
                        '<i class="fas fa-info-circle me-2"></i>' +
                        'Chi tiết đơn hàng #' + (orderId.length > 8 ? orderId.substring(0, 8) : orderId) + ' sẽ được hiển thị tại đây.' +
                        '</div>'
                    );
                }, 500);
            });
        });

        function showToast(message, type) {
            var bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';
            var toast = $('<div class="toast position-fixed top-0 end-0 m-3" style="z-index: 9999;">' +
                '<div class="toast-header ' + bgClass + ' text-white">' +
                '<strong class="me-auto">Thông báo</strong>' +
                '<button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>' +
                '</div>' +
                '<div class="toast-body">' + message + '</div>' +
                '</div>');
            
            $('body').append(toast);
            var toastEl = new bootstrap.Toast(toast[0]);
            toastEl.show();
            
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}
