# AdMob Test App

Ứng dụng Android demo tích hợp Google AdMob và Google Ads.

## Tính năng

- **Banner Ads**: Hi<PERSON>n thị quảng cáo banner ở cuối màn hình
- **Interstitial Ads**: Quảng cáo toàn màn hình có thể hiển thị giữa các hoạt động
- **Rewarded Ads**: <PERSON>u<PERSON><PERSON> cáo có thưởng cho người dùng

## Cấu hình

### 1. Thi<PERSON>t lập AdMob App ID

Trong file `AndroidManifest.xml`, thay thế App ID test bằng App ID thực của bạn:

```xml
<meta-data
    android:name="com.google.android.gms.ads.APPLICATION_ID"
    android:value="ca-app-pub-xxxxxxxxxxxxxxxx~yyyyyyyyyy"/>
```

### 2. <PERSON><PERSON><PERSON><PERSON> lậ<PERSON> Ad Unit IDs

Trong `MainActivity.kt`, thay thế các test Ad Unit IDs:

- **Banner Ad**: `ca-app-pub-3940256099942544/6300978111`
- **Interstitial Ad**: `ca-app-pub-3940256099942544/1033173712`
- **Rewarded Ad**: `ca-app-pub-3940256099942544/5224354917`

### 3. Cấu hình Google Services

1. Tạo project mới trên [Firebase Console](https://console.firebase.google.com/)
2. Thêm ứng dụng Android với package name: `com.example.admobtest`
3. Tải file `google-services.json` và thay thế file hiện tại trong thư mục `app/`

## Cài đặt và chạy

1. Mở project trong Android Studio
2. Sync project với Gradle files
3. Chạy ứng dụng trên thiết bị hoặc emulator

## Yêu cầu

- Android SDK 21+
- Android Studio Arctic Fox hoặc mới hơn
- Kết nối Internet để tải quảng cáo

## Cấu trúc project

```
app/
├── src/main/
│   ├── java/com/example/admobtest/
│   │   └── MainActivity.kt
│   ├── res/
│   │   ├── layout/
│   │   │   └── activity_main.xml
│   │   ├── values/
│   │   │   ├── colors.xml
│   │   │   ├── strings.xml
│   │   │   └── themes.xml
│   │   └── xml/
│   │       ├── backup_rules.xml
│   │       └── data_extraction_rules.xml
│   ├── AndroidManifest.xml
│   └── google-services.json
├── build.gradle
└── proguard-rules.pro
```

## Ghi chú

- Ứng dụng hiện tại sử dụng test Ad Unit IDs của Google
- Để sử dụng trong production, cần thay thế bằng Ad Unit IDs thực từ AdMob console
- Đảm bảo tuân thủ chính sách của Google AdMob khi phát hành ứng dụng
