using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using ShoeStoreWeb.Models;

namespace ShoeStoreWeb.Data;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public DbSet<Product> Products { get; set; }
    public DbSet<Order> Orders { get; set; }
    public DbSet<OrderItem> OrderItems { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // Configure Product entity
        builder.Entity<Product>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Price).HasPrecision(18, 2);
            entity.Property(e => e.OriginalPrice).HasPrecision(18, 2);
            entity.HasIndex(e => e.Category);
            entity.HasIndex(e => e.Brand);
            entity.HasIndex(e => e.Purpose);
            entity.HasIndex(e => e.Featured);
        });

        // Configure Order entity
        builder.Entity<Order>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Total).HasPrecision(18, 2);
            entity.HasOne(e => e.User)
                  .WithMany(u => u.Orders)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Restrict);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.CreatedAt);
        });

        // Configure OrderItem entity
        builder.Entity<OrderItem>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UnitPrice).HasPrecision(18, 2);
            entity.HasOne(e => e.Order)
                  .WithMany(o => o.Items)
                  .HasForeignKey(e => e.OrderId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.Product)
                  .WithMany(p => p.OrderItems)
                  .HasForeignKey(e => e.ProductId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // Configure ApplicationUser
        builder.Entity<ApplicationUser>(entity =>
        {
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Role).IsRequired().HasDefaultValue("customer");
        });

        // Seed data
        SeedData(builder);
    }

    private void SeedData(ModelBuilder builder)
    {
        // Seed some sample products
        var products = new[]
        {
            new Product
            {
                Id = "product-1",
                Name = "Nike Air Max 270",
                Brand = "Nike",
                Price = 3200000,
                OriginalPrice = 3500000,
                Description = "Giày thể thao Nike Air Max 270 với công nghệ đệm khí tiên tiến",
                Images = "[\"https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/99486859-0ff3-46b4-949b-2d16af2ad421/custom-nike-air-max-90.png\"]",
                Sizes = "[\"39\", \"40\", \"41\", \"42\", \"43\", \"44\"]",
                Colors = "[\"Đen\", \"Trắng\", \"Xám\"]",
                Category = "men",
                Purpose = "running",
                Featured = true,
                InStock = true,
                Rating = 4.5,
                Reviews = 128,
                CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
            },
            new Product
            {
                Id = "product-2",
                Name = "Adidas Ultraboost 22",
                Brand = "Adidas",
                Price = 4200000,
                Description = "Giày chạy bộ Adidas Ultraboost 22 với công nghệ Boost",
                Images = "[\"https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/fbaf991a78bc4896a3e9ad7800abcec6_9366/Ultraboost_22_Shoes_Black_GZ0127_01_standard.jpg\"]",
                Sizes = "[\"39\", \"40\", \"41\", \"42\", \"43\", \"44\"]",
                Colors = "[\"Đen\", \"Trắng\", \"Xanh\"]",
                Category = "men",
                Purpose = "running",
                Featured = true,
                InStock = true,
                Rating = 4.7,
                Reviews = 95,
                CreatedAt = new DateTime(2024, 1, 2, 0, 0, 0, DateTimeKind.Utc)
            },
            new Product
            {
                Id = "product-3",
                Name = "Converse Chuck Taylor All Star",
                Brand = "Converse",
                Price = 1800000,
                Description = "Giày sneaker cổ điển Converse Chuck Taylor All Star",
                Images = "[\"https://www.converse.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-cnv-master-catalog/default/dw2f8f0b9e/images/a_107/M7650_A_107X1.jpg\"]",
                Sizes = "[\"36\", \"37\", \"38\", \"39\", \"40\", \"41\", \"42\"]",
                Colors = "[\"Đen\", \"Trắng\", \"Đỏ\"]",
                Category = "women",
                Purpose = "casual",
                Featured = false,
                InStock = true,
                Rating = 4.3,
                Reviews = 67,
                CreatedAt = new DateTime(2024, 1, 3, 0, 0, 0, DateTimeKind.Utc)
            }
        };

        builder.Entity<Product>().HasData(products);
    }
}
